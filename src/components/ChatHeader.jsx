import theme from '../styles/theme';
// Import NBG design assets
import minimizeIcon from '../../design-assets/nbg-minimize-btn.png';
import closeIcon from '../../design-assets/nbg-close-icon.png';
import heroGraphics1 from '../../design-assets/nbg-hero-graphics-1.png';
import heroGraphics2 from '../../design-assets/nbg-hero-graphics-2.png';

/**
 * Chat header component following NBG design specification
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {string} props.error - Error message
 * @param {string} props.threadId - Current thread ID
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {Function} props.startNewConversation - Function to start a new conversation
 * @param {Function} props.logout - Function to log out
 * @param {Function} props.toggleDarkMode - Function to toggle dark mode
 * @param {Function} props.onMinimize - Function to minimize the chat
 * @param {Function} props.onClose - Function to close the chat
 * @returns {JSX.Element} - Rendered component
 */
const ChatHeader = ({
  isLoading,
  error,
  threadId,
  darkMode,
  startNewConversation,
  logout,
  toggleDarkMode,
  onMinimize,
  onClose
}) => {
  // NBG Header style following design specification
  const chatHeaderStyle = {
    position: 'relative',
    background: '#003841', // NBG primary dark color
    borderRadius: '4px 4px 0px 0px',
    padding: '16px 32px 16px 16px', // Following design spec
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    color: '#FFFFFF',
    overflow: 'hidden',
    minHeight: '72px' // Ensure consistent height
  };

  // Header frame (left side with profile and title)
  const headerFrameStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    zIndex: 2,
    position: 'relative'
  };

  // NBG Profile icon style following design specification
  const profileIconStyle = {
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: '#D5EFF4', // NBG profile background color
    border: '0.8px solid #003841', // NBG stroke
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#003841',
    fontSize: '18px',
    flexShrink: 0
  };

  // NBG Title style
  const titleStyle = {
    fontSize: '18px',
    fontWeight: 700,
    fontFamily: '"Aeonik Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    color: '#FFFFFF',
    lineHeight: '1.5em',
    margin: 0
  };

  // Header controls (right side)
  const headerControlsStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '32px',
    zIndex: 2,
    position: 'relative'
  };

  // Minimize button style
  const minimizeButtonStyle = {
    width: '16px',
    height: '16px',
    border: 'none',
    cursor: 'pointer',
    background: 'transparent',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  // Close icon style
  const closeIconStyle = {
    width: '16px',
    height: '16px',
    border: 'none',
    cursor: 'pointer',
    background: 'transparent',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  // Hero graphics styles
  const heroGraphics1Style = {
    position: 'absolute',
    top: '-208.92px',
    left: '52px',
    width: '788px',
    height: '328.33px',
    opacity: 0.4,
    zIndex: 1,
    pointerEvents: 'none'
  };

  const heroGraphics2Style = {
    position: 'absolute',
    top: '-87.92px',
    left: '-378px',
    width: '788px',
    height: '328.33px',
    opacity: 0.4,
    zIndex: 1,
    pointerEvents: 'none'
  };

  return (
    <div style={chatHeaderStyle}>
      {/* Hero Graphics Background Elements */}
      <img
        src={heroGraphics1}
        alt="Hero Graphics 1"
        style={heroGraphics1Style}
      />
      <img
        src={heroGraphics2}
        alt="Hero Graphics 2"
        style={heroGraphics2Style}
      />

      {/* Header Frame - Left side with profile icon and title */}
      <div style={headerFrameStyle}>
        <div style={profileIconStyle}>
          <i className="fas fa-robot" style={{ fontSize: '18px' }}></i>
        </div>
        <h3 style={titleStyle}>
          NBG Chat
        </h3>
      </div>

      {/* Header Controls - Right side with minimize and close buttons */}
      <div style={headerControlsStyle}>
        {/* Minimize button */}
        <button
          onClick={onMinimize || (() => {})}
          style={minimizeButtonStyle}
          title="Minimize"
        >
          <img
            src={minimizeIcon}
            alt="Minimize"
            style={{ width: '16px', height: 'auto' }}
          />
        </button>

        {/* Close button */}
        <button
          onClick={onClose || (() => {})}
          style={closeIconStyle}
          title="Close"
        >
          <img
            src={closeIcon}
            alt="Close"
            style={{ width: '16px', height: '16px' }}
          />
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;
