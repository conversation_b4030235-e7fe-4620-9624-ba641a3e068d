import theme from '../styles/theme';

/**
 * Chat header component
 * @param {Object} props - Component props
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {string} props.error - Error message
 * @param {string} props.threadId - Current thread ID
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {Function} props.startNewConversation - Function to start a new conversation
 * @param {Function} props.logout - Function to log out
 * @param {Function} props.toggleDarkMode - Function to toggle dark mode
 * @returns {JSX.Element} - Rendered component
 */
const ChatHeader = ({ 
  isLoading, 
  error, 
  threadId, 
  darkMode, 
  startNewConversation, 
  logout, 
  toggleDarkMode 
}) => {
  // Header style
  const chatHeaderStyle = {
    padding: theme.spacing.md,
    background: theme.colors.primary.gradient,
    color: theme.colors.text.light,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    transition: 'background 0.3s ease',
    boxShadow: 'none', // Remove shadow for cleaner look
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    marginBottom: 0,
    paddingBottom: theme.spacing.md
  };

  // Avatar style
  const avatarStyle = {
    width: '40px',
    height: '40px',
    borderRadius: theme.borderRadius.circle,
    backgroundColor: 'white',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '15px',
    boxShadow: theme.shadows.small,
    color: theme.colors.primary.main,
    border: '2px solid rgba(255, 255, 255, 0.8)',
    overflow: 'hidden',
    padding: 0,
    minWidth: '40px',
    minHeight: '40px',
    maxWidth: '40px',
    maxHeight: '40px'
  };

  // Header button style
  const headerButtonStyle = {
    marginRight: '10px',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    color: 'white',
    border: 'none',
    borderRadius: theme.borderRadius.circle,
    width: '32px',
    height: '32px',
    fontSize: '14px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: theme.transitions.normal
  };

  // Status indicator style
  const statusIndicatorStyle = {
    width: '12px',
    height: '12px',
    borderRadius: theme.borderRadius.circle,
    backgroundColor: error 
      ? theme.colors.status.error 
      : (isLoading ? theme.colors.status.warning : theme.colors.status.success),
    boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.3)',
    transition: theme.transitions.normal
  };

  return (
    <div style={chatHeaderStyle}>
      <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
        <div style={{display: 'flex', alignItems: 'center'}}>
          <div style={avatarStyle}>
            <i className="fas fa-robot" style={{ fontSize: '18px' }}></i>
          </div>
          <div>
            <h3 style={{margin: 0, fontSize: theme.typography.fontSize.xl, fontWeight: theme.typography.fontWeight.semiBold}}>
              AI Assistant
            </h3>
            <p style={{margin: '4px 0 0', fontSize: theme.typography.fontSize.sm, opacity: 0.9}}>
              {isLoading ? (
                <span className="typing-animation">
                  Typing<span className="dot">.</span><span className="dot">.</span><span className="dot">.</span>
                </span>
              ) : 'Online'}
              <span style={{
                display: 'inline-block', 
                marginLeft: '5px', 
                width: '8px', 
                height: '8px', 
                borderRadius: theme.borderRadius.circle, 
                backgroundColor: isLoading ? theme.colors.status.warning : theme.colors.status.success
              }}></span>
            </p>
          </div>
        </div>
        <div style={{display: 'flex', alignItems: 'center'}}>
          {/* New Chat button */}
          <button
            onClick={startNewConversation}
            style={headerButtonStyle}
            title="Start New Conversation"
          >
            <i className="fas fa-sync-alt"></i>
          </button>
          
          {/* Logout button */}
          <button
            onClick={logout}
            style={headerButtonStyle}
            title="Logout"
          >
            <i className="fas fa-sign-out-alt"></i>
          </button>
          
          {/* Dark mode toggle button */}
          <button
            onClick={toggleDarkMode}
            style={headerButtonStyle}
            title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
          >
            <i className={`fas fa-${darkMode ? 'sun' : 'moon'}`}></i>
          </button>
          
          {/* Status indicator */}
          <div
            style={statusIndicatorStyle}
            title={error ? 'Error' : (isLoading ? 'Processing' : 'Connected')}
          />
        </div>
      </div>
      <p style={{margin: '5px 0 0', fontSize: theme.typography.fontSize.xs, opacity: 0.8}}>
        {error
          ? 'Connection error: ' + error
          : (isLoading
             ? 'Processing...'
             : (threadId
                ? `Connected`
                : 'Ready to chat'))}
      </p>
    </div>
  );
};

export default ChatHeader;
