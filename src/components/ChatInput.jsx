import { useState, useRef, useEffect } from 'react';
import EmojiPicker from 'emoji-picker-react';
import theme from '../styles/theme';
// Import NBG send icon
import sendIcon from '../../design-assets/nbg-send-icon.png';

/**
 * Chat input component
 * @param {Object} props - Component props
 * @param {string} props.inputValue - Current input value
 * @param {Function} props.setInputValue - Function to update input value
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {Function} props.handleSendMessage - Function to handle sending message
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @returns {JSX.Element} - Rendered component
 */
const ChatInput = ({
  inputValue,
  setInputValue,
  isLoading,
  setIsLoading,
  handleSendMessage,
  darkMode
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef(null);

  // NBG Footer container style following design specification
  const inputContainerStyle = {
    display: 'flex',
    flexDirection: 'column',
    padding: '16px 24px', // NBG footer padding
    backgroundColor: darkMode ? theme.colors.dark.background : '#FFFFFF', // NBG footer background
    boxShadow: darkMode ? 'none' : '0px 6px 15px 5px rgba(0, 56, 65, 0.1)', // NBG footer shadow
    gap: '14px' // NBG footer gap
  };

  // NBG Input area style
  const inputAreaStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '16px' // NBG input area gap
  };

  // NBG Input style following design specification
  const inputStyle = {
    flex: 1,
    padding: '12px 16px', // NBG input padding
    border: 'none',
    outline: 'none',
    fontSize: '14px', // NBG input font size
    fontWeight: 400, // NBG input font weight
    lineHeight: '1.5em', // NBG input line height
    backgroundColor: 'transparent',
    color: darkMode ? theme.colors.dark.text : '#6A6C6A', // NBG secondary text color
    fontFamily: theme.typography.fontFamily,
    borderRadius: 0 // Remove border radius for clean look
  };

  // NBG Send button style following design specification
  const sendButtonStyle = {
    width: '24px', // NBG send icon dimensions
    height: '24px',
    backgroundColor: '#FFFFFF', // NBG send icon background
    border: 'none',
    cursor: isLoading ? 'not-allowed' : 'pointer',
    opacity: isLoading ? 0.7 : 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: theme.transitions.fast,
    borderRadius: 0,
    padding: 0
  };

  // Stop button style
  const stopButtonStyle = {
    ...sendButtonStyle,
    backgroundColor: theme.colors.status.error,
    boxShadow: '0 2px 8px rgba(220, 53, 69, 0.3)',
  };

  // Handle input change
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  // Handle emoji selection
  const handleEmojiClick = (emojiObject) => {
    setInputValue(prevInput => prevInput + emojiObject.emoji);
    setShowEmojiPicker(false); // Hide picker after selection
  };

  // Toggle emoji picker
  const toggleEmojiPicker = (e) => {
    e.preventDefault();
    setShowEmojiPicker(!showEmojiPicker);
  };

  // Handle click outside emoji picker
  useEffect(() => {
    function handleClickOutside(event) {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
    }

    // Add event listener when emoji picker is shown
    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  return (
    <div style={inputContainerStyle}>
      {/* Input Area */}
      <form onSubmit={handleSendMessage} style={inputAreaStyle}>
        {/* Input field */}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder="Γράψτε ένα μικρό και απλό μήνυμα" // NBG Greek placeholder
          style={inputStyle}
          disabled={isLoading}
          className="chat-input"
        />

        {/* Send button */}
        {isLoading ? (
          <button
            type="button"
            onClick={() => setIsLoading(false)}
            style={stopButtonStyle}
          >
            <i className="fas fa-times"></i>
          </button>
        ) : (
          <button
            type="submit"
            style={sendButtonStyle}
            disabled={isLoading}
          >
            <img
              src={sendIcon}
              alt="Send"
              style={{ width: '24px', height: '24px' }}
            />
          </button>
        )}
      </form>

      {/* Functionalities - Attachment */}
      <div style={{
        display: 'flex',
        gap: '12px' // NBG functionalities gap
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px', // NBG attachment gap
          cursor: 'pointer'
        }}>
          {/* Attachment icon */}
          <div style={{
            width: '16px',
            height: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M8.66667 7.33333L4 12C3.26667 12.7333 2.13333 12.7333 1.4 12C0.666667 11.2667 0.666667 10.1333 1.4 9.4L8.66667 2.13333C9.8 1 11.7333 1 12.8667 2.13333C14 3.26667 14 5.2 12.8667 6.33333L5.6 13.6C5.06667 14.1333 4.2 14.1333 3.66667 13.6C3.13333 13.0667 3.13333 12.2 3.66667 11.6667L10.2667 5.06667"
                stroke="#007B85" // NBG attachment color
                strokeWidth="1.33"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <span style={{
            fontSize: '12px', // NBG attachment text size
            fontWeight: 400,
            color: '#007B85', // NBG attachment color
            lineHeight: '1.3em'
          }}>
            Attachment
          </span>
        </div>
      </div>

      {/* Emoji picker */}
      {showEmojiPicker && (
        <div
          ref={emojiPickerRef}
          style={{
            position: 'absolute',
            bottom: '120px',
            left: '24px',
            scale: '80%',
            zIndex: theme.zIndex.dropdown
          }}
        >
          <EmojiPicker
            onEmojiClick={handleEmojiClick}
            width={300}
            height={400}
            previewConfig={{ showPreview: false }}
            searchDisabled
          />
        </div>
      )}
    </div>
  );
};

export default ChatInput;
