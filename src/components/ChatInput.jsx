import { useState, useRef, useEffect } from 'react';
import EmojiPicker from 'emoji-picker-react';
import theme from '../styles/theme';

/**
 * Chat input component
 * @param {Object} props - Component props
 * @param {string} props.inputValue - Current input value
 * @param {Function} props.setInputValue - Function to update input value
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {Function} props.handleSendMessage - Function to handle sending message
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @returns {JSX.Element} - Rendered component
 */
const ChatInput = ({
  inputValue,
  setInputValue,
  isLoading,
  setIsLoading,
  handleSendMessage,
  darkMode
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef(null);

  // Input container style
  const inputContainerStyle = {
    display: 'flex',
    padding: '15px 20px',
    borderTop: darkMode
      ? `1px solid ${theme.colors.dark.border}`
      : `1px solid ${theme.colors.light.border}`,
    backgroundColor: darkMode
      ? theme.colors.dark.background
      : theme.colors.light.background,
    alignItems: 'center'
  };

  // Input style
  const inputStyle = {
    flex: 1,
    padding: '12px 18px',
    paddingLeft: '40px', // Make room for emoji button on the left
    paddingRight: '10px', // Reduced padding on right so text doesn't go under button
    borderRadius: '24px',
    border: darkMode
      ? `1px solid ${theme.colors.dark.border}`
      : `1px solid ${theme.colors.light.border}`,
    outline: 'none',
    fontSize: theme.typography.fontSize.md,
    boxShadow: 'none',
    transition: theme.transitions.fast,
    backgroundColor: darkMode
      ? theme.colors.dark.input
      : theme.colors.light.input,
    color: darkMode
      ? theme.colors.dark.text
      : theme.colors.light.text,
    fontWeight: theme.typography.fontWeight.regular,
    width: 'calc(100% - 55px)', // Adjust width for proper alignment
    marginRight: '0', // Remove margin
    position: 'absolute', // Position absolutely
    left: '0', // Align to the left
    top: '50%', // Center vertically
    transform: 'translateY(-50%)' // Perfect vertical centering
  };

  // Send button style
  const sendButtonStyle = {
    position: 'absolute',
    right: '0', // Keep button at the far right
    top: '50%',
    transform: 'translateY(-50%)',
    width: '40px',
    height: '40px',
    backgroundColor: isLoading
      ? 'rgba(11, 45, 122, 0.5)'
      : theme.colors.primary.main,
    color: theme.colors.text.light,
    border: 'none',
    borderRadius: theme.borderRadius.circle,
    cursor: isLoading ? 'not-allowed' : 'pointer',
    opacity: isLoading ? 0.7 : 1,
    boxShadow: theme.shadows.small,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: theme.typography.fontWeight.bold,
    transition: theme.transitions.fast,
    fontSize: '18px'
  };

  // Stop button style
  const stopButtonStyle = {
    ...sendButtonStyle,
    backgroundColor: theme.colors.status.error,
    boxShadow: '0 2px 8px rgba(220, 53, 69, 0.3)',
  };

  // Handle input change
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  // Handle emoji selection
  const handleEmojiClick = (emojiObject) => {
    setInputValue(prevInput => prevInput + emojiObject.emoji);
    setShowEmojiPicker(false); // Hide picker after selection
  };

  // Toggle emoji picker
  const toggleEmojiPicker = (e) => {
    e.preventDefault();
    setShowEmojiPicker(!showEmojiPicker);
  };

  // Handle click outside emoji picker
  useEffect(() => {
    function handleClickOutside(event) {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
    }

    // Add event listener when emoji picker is shown
    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  return (
    <form onSubmit={handleSendMessage} style={{...inputContainerStyle, padding: '12px'}}>
      <div style={{ position: 'relative', width: '100%', height: '44px' }}>
        {/* Emoji button */}
        <button
          type="button"
          onClick={toggleEmojiPicker}
          style={{
            position: 'absolute',
            left: '15px',
            top: '50%',
            transform: 'translateY(-50%)',
            background: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '18px',
            color: darkMode ? '#aaa' : '#888',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '0',
            zIndex: 2,
            pointerEvents: isLoading ? 'none' : 'auto',
            opacity: isLoading ? 0.5 : 1
          }}
          disabled={isLoading}
        >
          <i className="fas fa-smile"></i>
        </button>

        {/* Input field */}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder="Type your message..."
          style={inputStyle}
          disabled={isLoading}
          className="chat-input"
        />

        {/* Send or stop button */}
        {isLoading ? (
          <button
            type="button"
            onClick={() => setIsLoading(false)}
            style={stopButtonStyle}
          >
            <i className="fas fa-times"></i>
          </button>
        ) : (
          <button
            type="submit"
            style={sendButtonStyle}
            disabled={isLoading}
          >
            <i className="fas fa-paper-plane"></i>
          </button>
        )}

        {/* Emoji picker */}
        {showEmojiPicker && (
          <div
            ref={emojiPickerRef}
            style={{
              position: 'absolute',
              bottom: '50px',
              left: '0',
              scale: '80%',
              zIndex: theme.zIndex.dropdown
            }}
          >
            <EmojiPicker
              onEmojiClick={handleEmojiClick}
              width={300}
              height={400}
              previewConfig={{ showPreview: false }}
              searchDisabled
            />
          </div>
        )}
      </div>

      {/* Add a style tag for input focus state */}
      <style>{`
        .chat-input:focus {
          border-color: ${theme.colors.primary.main};
          box-shadow: 0 0 0 2px rgba(11, 45, 122, 0.2);
          background-color: ${darkMode ? '#4a4a5c' : '#ffffff'};
        }
        .chat-input::placeholder {
          color: ${darkMode ? '#aaa' : '#888'};
          opacity: 0.8;
        }
        .resize-handle:hover {
          background-color: ${darkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.08)'};
        }
        .resize-handle:hover > div > div {
          background-color: ${darkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.4)'};
        }
        .resize-handle:active {
          background-color: ${darkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.12)'};
        }
        .resize-handle:active > div > div {
          background-color: ${darkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.6)'};
        }
        body.resizing {
          cursor: ew-resize !important;
          user-select: none;
        }
        /* Timestamp hover effect */
        .timestamp-display:hover {
          background-color: ${darkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.1)'};
          color: ${darkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'};
        }
        /* Markdown styles */
        a {
          color: ${darkMode ? '#8ab4f8' : theme.colors.primary.main};
          text-decoration: underline;
        }
        code {
          background-color: ${darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)'};
          padding: 0.2em 0.4em;
          border-radius: 3px;
          font-family: monospace;
          font-size: 0.9em;
        }
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }
        @keyframes fadeIn {
          0% { opacity: 0; }
          100% { opacity: 1; }
        }
        @keyframes blink {
          0% { opacity: 0.3; }
          50% { opacity: 1; }
          100% { opacity: 0.3; }
        }
        .fa-robot, .fa-comment, .fa-paper-plane, .fa-times, .fa-sync-alt, .fa-bell {
          animation: fadeIn 0.3s ease-in-out;
        }
        .fa-paper-plane:hover, .fa-sync-alt:hover {
          transform: scale(1.1);
          transition: transform 0.2s ease;
        }
        .typing-animation .dot {
          animation: blink 1.4s infinite;
          display: inline-block;
        }
        .typing-animation .dot:nth-child(2) {
          animation-delay: 0.2s;
        }
        .typing-animation .dot:nth-child(3) {
          animation-delay: 0.4s;
        }
      `}</style>
    </form>
  );
};

export default ChatInput;
