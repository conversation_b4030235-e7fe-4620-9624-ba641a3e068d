import { useRef, useEffect } from 'react';
import MessageBubble from './MessageBubble';
import theme from '../styles/theme';

/**
 * Chat messages container component
 * @param {Object} props - Component props
 * @param {Array} props.messages - Array of message objects
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @returns {JSX.Element} - Rendered component
 */
const ChatMessages = ({ messages, darkMode }) => {
  const messagesEndRef = useRef(null);

  // Messages container style - following NBG design specification
  const messagesContainerStyle = {
    flex: 1,
    padding: '8px 16px', // NBG padding specification
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px', // NBG gap specification
    backgroundColor: darkMode ? theme.colors.dark.surface : '#F5F8F6', // NBG main area background
    borderTop: 'none',
    marginTop: 0
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add placeholder welcome message if no messages exist - using NBG Greek text
  const displayMessages = messages.length > 0
    ? messages
    : [{
        id: 'welcome-' + Date.now(),
        text: 'Καλημέρα! Είμαι ο ψηφιακός βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;',
        isUser: false,
        timestamp: null // No timestamp for welcome message
      }];

  return (
    <div style={messagesContainerStyle}>
      {/* Messages */}
      {displayMessages.map((message, index) => (
        <MessageBubble
          key={`${message.id}-${index}`}
          message={message}
          darkMode={darkMode}
        />
      ))}

      {/* Invisible element for scrolling to bottom */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;
