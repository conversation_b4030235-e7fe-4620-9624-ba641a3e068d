import { useRef, useEffect } from 'react';
import MessageBubble from './MessageBubble';
import theme from '../styles/theme';

/**
 * Chat messages container component
 * @param {Object} props - Component props
 * @param {Array} props.messages - Array of message objects
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @returns {JSX.Element} - Rendered component
 */
const ChatMessages = ({ messages, darkMode }) => {
  const messagesEndRef = useRef(null);

  // Messages container style
  const messagesContainerStyle = {
    flex: 1,
    padding: '25px 20px 20px 20px', // Extra padding at the top
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    backgroundColor: darkMode ? theme.colors.dark.surface : theme.colors.light.surface,
    borderTop: 'none',
    marginTop: 0,
    position: 'relative',
    zIndex: 0 // Lower than the wave
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add placeholder welcome message if no messages exist
  const displayMessages = messages.length > 0
    ? messages
    : [{
        id: 'welcome-' + Date.now(),
        text: 'Welcome! Enter a message to start chatting.',
        isUser: false,
        timestamp: null // No timestamp for welcome message
      }];

  return (
    <div style={messagesContainerStyle}>
      {/* Wave decoration */}
      <div style={{
        position: 'absolute',
        top: -30, // Position above the container
        left: 0,
        width: '100%',
        height: '30px',
        overflow: 'hidden',
        zIndex: 1
      }}>
        <svg
          style={{
            position: 'absolute',
            bottom: '-1px',
            left: 0,
            width: '100%',
            height: '31px',
            display: 'block'
          }}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1000 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0 C200,40 350,40 500,20 C650,0 800,10 1000,30 L1000,100 L0,100 Z"
            fill={darkMode ? theme.colors.dark.surface : theme.colors.light.surface}
          />
        </svg>
      </div>

      {/* Messages */}
      {displayMessages.map((message, index) => (
        <MessageBubble 
          key={`${message.id}-${index}`} 
          message={message} 
          darkMode={darkMode} 
        />
      ))}

      {/* Invisible element for scrolling to bottom */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessages;
