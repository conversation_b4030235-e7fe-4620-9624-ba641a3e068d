import { useState, useEffect, useRef } from 'react';
import theme from '../styles/theme';
import '../styles/global.css';
import { getStorageItem, setStorageItem, removeStorageItem, STORAGE_KEYS } from '../utils/storageUtils';
import { convertEmoticonsToEmojis } from '../utils/messageUtils';
import { sendMessageToThread, generateThreadId, sendFeedback } from '../utils/apiUtils';

// Import components
import ChatButton from './ChatButton';
import ChatHeader from './ChatHeader';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import CaptchaOverlay from './CaptchaOverlay';
import ResizeHandle from './ResizeHandle';
import FeedbackWidget from './FeedbackWidget';

/**
 * Main Chat Widget component
 * @param {Object} props - Component props
 * @param {string} props.apiUrl - API URL for backend communication
 * @returns {JSX.Element} - Rendered component
 */
const ChatWidget = ({ apiUrl }) => {
  // Captcha state
  const [captchaPassed, setCaptchaPassed] = useState(false);
  const [captchaError, setCaptchaError] = useState(null);

  // Get initial values from localStorage if available
  const [isOpen, setIsOpen] = useState(() => getStorageItem(STORAGE_KEYS.IS_OPEN, false));
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState(() => getStorageItem(STORAGE_KEYS.MESSAGES, []));
  const [threadId, setThreadId] = useState(() => getStorageItem(STORAGE_KEYS.THREAD_ID, null));
  const [darkMode, setDarkMode] = useState(() => getStorageItem(STORAGE_KEYS.DARK_MODE, false));
  const [windowWidth, setWindowWidth] = useState(() => getStorageItem(STORAGE_KEYS.WINDOW_WIDTH, 320));
  const [isResizing, setIsResizing] = useState(false);
  const [feedbackData, setFeedbackData] = useState(() => getStorageItem(STORAGE_KEYS.FEEDBACK, {}));
  const chatContainerRef = useRef(null);

  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Base URL for API requests - use provided apiUrl or fallback to env variable
  const API_URL = apiUrl || import.meta.env.VITE_API_URL;

  // Start a new conversation
  const startNewConversation = () => {
    console.log("Starting new conversation...");

    // Clear all messages and thread data
    setMessages([]);
    setThreadId(null);
    removeStorageItem(STORAGE_KEYS.THREAD_ID);
    removeStorageItem(STORAGE_KEYS.MESSAGES);

    // Generate new thread ID
    createThread();

    // Add welcome message
    const welcomeMessage = [{
      id: 'welcome',
      text: 'Καλωσήρθατε! Είμαι ο έξυπνος προσωπικός σας βοηθός, πώς μπορώ να σας βοηθήσω;',
      isUser: false,
      timestamp: null
    }];
    setMessages(welcomeMessage);
    setStorageItem(STORAGE_KEYS.MESSAGES, welcomeMessage);
  };

  // Handle feedback submission
  const handleSubmitFeedback = async (threadId, rating, comment) => {
    try {
      // Send feedback to backend
      await sendFeedback(API_URL, threadId, rating, comment);

      // Store feedback in local storage
      const updatedFeedbackData = {
        ...feedbackData,
        [threadId]: { rating, comment, timestamp: new Date().toISOString() }
      };

      setFeedbackData(updatedFeedbackData);
      setStorageItem(STORAGE_KEYS.FEEDBACK, updatedFeedbackData);

      return true;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  };

  // Generate new thread ID
  const createThread = () => {
    const newThreadId = generateThreadId();
    console.log('Created new thread ID:', newThreadId);
    setThreadId(newThreadId);
    setStorageItem(STORAGE_KEYS.THREAD_ID, newThreadId);
    return newThreadId;
  };

  // Clear all chat data (logout)
  const logout = () => {
    console.log("Logging out...");

    // Clear all chat-related data from localStorage
    removeStorageItem(STORAGE_KEYS.THREAD_ID);
    removeStorageItem(STORAGE_KEYS.MESSAGES);
    removeStorageItem(STORAGE_KEYS.IS_OPEN);

    // Reset states
    setMessages([]);
    setThreadId(null);
    setIsOpen(false);

    // Add logout confirmation message
    const logoutMessage = [{
      id: 'logout',
      text: 'You have been logged out. Start a new conversation to continue.',
      isUser: false,
      timestamp: null
    }];
    setMessages(logoutMessage);
  };

  // Handle incoming agent message
  const handleAgentMessage = (agentMessage) => {
    console.log('Received agent message:', agentMessage);
    setMessages(prev => {
      const filteredMessages = prev.filter(msg => msg.text !== "Processing...");
      const newMessages = [
        ...filteredMessages,
        {
          id: Date.now(),
          text: agentMessage,
          isUser: false,
          timestamp: new Date().toISOString()
        }
      ];
      setStorageItem(STORAGE_KEYS.MESSAGES, newMessages);
      return newMessages;
    });
  };

  // Toggle chat open/closed
  const toggleChat = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    setStorageItem(STORAGE_KEYS.IS_OPEN, newIsOpen);
  };

  // Toggle dark mode
  const toggleDarkMode = (e) => {
    e.preventDefault();
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    setStorageItem(STORAGE_KEYS.DARK_MODE, newDarkMode);
  };

  // Handle sending a message
  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (inputValue.trim() === '') return;

    const convertedText = convertEmoticonsToEmojis(inputValue);
    setIsLoading(true);
    setError(null);

    try {
      // Add user message to chat
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now(),
            text: convertedText,
            isUser: true,
            timestamp: new Date().toISOString()
          }
        ];
        setStorageItem(STORAGE_KEYS.MESSAGES, newMessages);
        return newMessages;
      });

      // Add processing message
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now() + 1,
            text: "Processing...",
            isUser: false,
            timestamp: null
          }
        ];
        setStorageItem(STORAGE_KEYS.MESSAGES, newMessages);
        return newMessages;
      });

      // Get or create thread ID
      const currentThreadId = threadId || createThread();
      if (!currentThreadId) {
        throw new Error('Failed to generate thread ID');
      }

      // Send message to thread
      const response = await sendMessageToThread(API_URL, currentThreadId, convertedText);

      // Handle agent response
      if (response && response.agentMessage) {
        handleAgentMessage(response.agentMessage);
      }

    } catch (error) {
      console.error('Error in handleSendMessage:', error);
      setError(error.message);
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now(),
            text: `Error: ${error.message || 'Failed to send message'}`,
            isUser: false,
            timestamp: null
          }
        ];
        setStorageItem(STORAGE_KEYS.MESSAGES, newMessages);
        return newMessages;
      });
    } finally {
      setIsLoading(false);
      setInputValue('');
    }
  };

  // Handle resize start
  const handleResizeStart = (e) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling

    setIsResizing(true);

    // Add a class to the body to change cursor during resize
    document.body.classList.add('resizing');

    // Add event listeners for mouse/touch move and end
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
    document.addEventListener('touchmove', handleResizeMove, { passive: false });
    document.addEventListener('touchend', handleResizeEnd);
    document.addEventListener('touchcancel', handleResizeEnd);
  };

  // Handle resize during mouse move
  const handleResizeMove = (e) => {
    if (!isResizing || !chatContainerRef.current) return;

    // Prevent default behavior to avoid scrolling on mobile
    if (e.cancelable) e.preventDefault();

    // Get the client X position from either mouse or touch event
    const clientX = e.touches && e.touches[0] ? e.touches[0].clientX : e.clientX;

    // Calculate new width based on mouse/touch position
    const chatWidgetRect = chatContainerRef.current.getBoundingClientRect();
    const newWidth = Math.round(chatWidgetRect.right - clientX);

    // Set minimum and maximum width constraints
    const minWidth = 280;
    const maxWidth = 600;

    // Check if the new width would cause the left edge to go off-screen
    const windowWidth = window.innerWidth;
    const rightEdgePosition = chatWidgetRect.right;
    const leftEdgePosition = rightEdgePosition - newWidth;

    // Ensure the left edge doesn't go off-screen (with a 20px margin)
    if (leftEdgePosition < 20) {
      return;
    }

    // Apply the new width if it's within constraints
    if (newWidth >= minWidth && newWidth <= maxWidth) {
      // Apply the width directly to the element for immediate feedback
      chatContainerRef.current.style.width = `${newWidth}px`;
      // Update state
      setWindowWidth(newWidth);
    }
  };

  // Handle resize end
  const handleResizeEnd = () => {
    if (!isResizing) return;

    setIsResizing(false);

    // Remove the resizing class from body
    document.body.classList.remove('resizing');

    // Save the new width to localStorage
    setStorageItem(STORAGE_KEYS.WINDOW_WIDTH, windowWidth);

    // Remove event listeners
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
    document.removeEventListener('touchmove', handleResizeMove);
    document.removeEventListener('touchend', handleResizeEnd);
    document.removeEventListener('touchcancel', handleResizeEnd);
  };

  // Widget container style
  const widgetStyle = {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    zIndex: theme.zIndex.widget,
    fontFamily: theme.typography.fontFamily
  };

  // Chat window style
  const chatWindowStyle = {
    position: 'absolute',
    bottom: '80px',
    right: '0',
    width: `${windowWidth}px`,
    height: '650px',
    backgroundColor: darkMode ? theme.colors.dark.background : theme.colors.light.background,
    borderRadius: theme.borderRadius.large,
    boxShadow: darkMode ? '0 10px 25px rgba(0, 0, 0, 0.3)' : '0 10px 25px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    flexDirection: 'column',
    zIndex: theme.zIndex.widget,
    overflow: 'hidden',
    border: darkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.08)',
    transition: isResizing ? 'none' : theme.transitions.bounce
  };

  return (
    <div style={widgetStyle}>
      {isOpen && (
        <div
          ref={chatContainerRef}
          className={`chat-widget-container ${darkMode ? 'dark-mode' : ''}`}
          style={chatWindowStyle}>
          {/* Captcha overlay */}
          <CaptchaOverlay
            isOpen={isOpen}
            captchaPassed={captchaPassed}
            captchaError={captchaError}
            setCaptchaPassed={setCaptchaPassed}
            setCaptchaError={setCaptchaError}
          />

          {/* Resize handle */}
          <ResizeHandle
            darkMode={darkMode}
            onResizeStart={handleResizeStart}
          />

          {/* Chat header */}
          <ChatHeader
            isLoading={isLoading}
            error={error}
            threadId={threadId}
            darkMode={darkMode}
            startNewConversation={startNewConversation}
            logout={logout}
            toggleDarkMode={toggleDarkMode}
          />

          {/* Wave decoration */}
          <div style={{
            position: 'relative',
            height: '30px',
            background: theme.colors.primary.gradient,
            marginTop: '-1px',
            overflow: 'hidden',
            zIndex: 1
          }}>
            <svg
              style={{
                position: 'absolute',
                bottom: '-1px',
                left: 0,
                width: '100%',
                height: '31px',
                display: 'block'
              }}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1000 100"
              preserveAspectRatio="none"
            >
              <path
                d="M0,0 C200,40 350,40 500,20 C650,0 800,10 1000,30 L1000,100 L0,100 Z"
                fill={darkMode ? theme.colors.dark.surface : theme.colors.light.surface}
              />
            </svg>
          </div>

          {/* Chat messages */}
          <ChatMessages
            messages={messages}
            darkMode={darkMode}
          />

          {/* Feedback widget - only show when there's an active thread ID and it's not the welcome message */}
          {threadId && messages.length > 1 && (
            <FeedbackWidget
              threadId={threadId}
              onSubmitFeedback={handleSubmitFeedback}
              darkMode={darkMode}
              feedbackData={feedbackData}
            />
          )}

          {/* Chat input */}
          <ChatInput
            inputValue={inputValue}
            setInputValue={setInputValue}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            handleSendMessage={handleSendMessage}
            darkMode={darkMode}
          />
        </div>
      )}

      {/* Chat button */}
      <ChatButton
        isOpen={isOpen}
        hasError={!!error}
        onClick={toggleChat}
        unreadCount={messages.filter(msg => !msg.isUser).length}
      />
    </div>
  );
};

export default ChatWidget;
