/**
 * Hero Graphics component for NBG Chat Header
 * Provides decorative background elements following NBG design specification
 */
const HeroGraphics = ({ variant = 1, style = {} }) => {
  // Base SVG pattern for hero graphics
  const HeroPattern = ({ opacity = 0.4, transform = "" }) => (
    <svg
      width="788"
      height="328"
      viewBox="0 0 788 328"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        position: 'absolute',
        opacity,
        transform,
        pointerEvents: 'none'
      }}
    >
      {/* Geometric pattern representing NBG brand elements */}
      <g opacity={opacity}>
        {/* Main geometric shapes */}
        <circle cx="200" cy="100" r="80" fill="url(#gradient1)" />
        <circle cx="400" cy="200" r="60" fill="url(#gradient2)" />
        <circle cx="600" cy="120" r="70" fill="url(#gradient1)" />
        
        {/* Connecting lines */}
        <path
          d="M200 100 Q300 150 400 200 Q500 160 600 120"
          stroke="url(#gradient3)"
          strokeWidth="2"
          fill="none"
        />
        
        {/* Additional decorative elements */}
        <rect x="150" y="250" width="40" height="40" rx="8" fill="url(#gradient2)" />
        <rect x="350" y="80" width="30" height="30" rx="6" fill="url(#gradient1)" />
        <rect x="550" y="280" width="35" height="35" rx="7" fill="url(#gradient3)" />
      </g>
      
      {/* Gradient definitions */}
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#007B85" stopOpacity="0.6" />
          <stop offset="100%" stopColor="#00626A" stopOpacity="0.3" />
        </linearGradient>
        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#00626A" stopOpacity="0.5" />
          <stop offset="100%" stopColor="#007B85" stopOpacity="0.2" />
        </linearGradient>
        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#D5EFF4" stopOpacity="0.4" />
          <stop offset="100%" stopColor="#007B85" stopOpacity="0.1" />
        </linearGradient>
      </defs>
    </svg>
  );

  if (variant === 1) {
    return (
      <div style={{
        position: 'absolute',
        top: '-208.92px',
        left: '52px',
        width: '788px',
        height: '328.33px',
        zIndex: 1,
        pointerEvents: 'none',
        ...style
      }}>
        <HeroPattern opacity={0.4} />
      </div>
    );
  }

  if (variant === 2) {
    return (
      <div style={{
        position: 'absolute',
        top: '-87.92px',
        left: '-378px',
        width: '788px',
        height: '328.33px',
        zIndex: 1,
        pointerEvents: 'none',
        ...style
      }}>
        <HeroPattern opacity={0.4} transform="scale(-1, 1)" />
      </div>
    );
  }

  return null;
};

export default HeroGraphics;
